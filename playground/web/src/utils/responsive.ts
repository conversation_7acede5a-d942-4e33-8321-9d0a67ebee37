const rpxConfig = [
  {
    query: '(max-width: 649.9px)',
    padding: 32,
    base: 360,
  },
  {
    query: '(min-width: 650px) and (max-width: 1023.9px)',
    padding: 48,
    base: 768,
  },
  {
    query: '(min-width: 1024px) and (max-width: 1439.9px)',
    padding: 128,
    base: 1440,
  },
]

export function rpx(value: number) {
  for(const {query, base, padding} of rpxConfig) {
    if (matchMedia(query).matches) {
      return value * (vw(100) - padding ) / (base - padding)
    }
  }
  return value
}

export function vw(value: number) {
  return value * window.innerWidth / 100
}

export function vh(value: number) {
  return value * window.innerHeight / 100
}

function round(num: number, length: number) {
  if (length > 0) {
    const N = 10 ** length
    return Math.round(num * N + Number.EPSILON) / N
  }
  return Math.round(num)
}
